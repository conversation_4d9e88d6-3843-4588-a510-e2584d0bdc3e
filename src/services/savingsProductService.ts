import mongoose from "mongoose";
import {
  currenciesConfig,
  fees as feesConfig,
  localeConfig,
  plansConfig,
  savingsUniverseConfig
} from "@wealthyhood/shared-configs";
import logger from "../external-services/loggerService";
import DbUtil from "../utils/dbUtil";
import { SavingsProduct, SavingsProductDocument } from "../models/SavingsProduct";
import { OrderSideType } from "../external-services/wealthkernelService";
import SubscriptionService from "./subscriptionService";
import Decimal from "decimal.js";
import DateUtil from "../utils/dateUtil";
import UserService from "./userService";
import ConfigUtil from "../utils/configUtil";
import { SubscriptionDocument } from "../models/Subscription";
import { PortfolioDocument } from "../models/Portfolio";
import { DailyPortfolioSavingsTicker } from "../models/DailyTicker";
import {
  SavingsDividendTransaction,
  SavingsTopupTransactionDocument,
  SavingsWithdrawalTransactionDocument
} from "../models/Transaction";
import { UserDocument } from "../models/User";
import CurrencyUtil from "../utils/currencyUtil";
import { TransactionService } from "./transactionService";
import { formatPercentage } from "../utils/formatterUtil";
import { captureException } from "@sentry/node";

const { SAVINGS_PRODUCT_CONFIG_GLOBAL } = savingsUniverseConfig;
const { SAVINGS_PRODUCT_FEE_RATES } = feesConfig;

const SavingsProductDictKeyArray = ["commonId", "isin"] as const;
type SavingsProductDictKeyType = (typeof SavingsProductDictKeyArray)[number];

type FeeDetailsPerPlanType = {
  fundManagerAnnualFeePercentage: string;
  wealthyhoodAnnualFeePercentage: string;
  netInterestRate: string;
  netInterestRateValue: number;
  plan: plansConfig.PlanType;
};
export type SavingsProductFeeDetailsType = {
  grossInterestRate: string;
  netInterestRateOfCurrentPlan?: string;
  planColumnLabel: string;
  fundManagerFeeColumnLabel: string;
  wealthyhoodFeeColumnLabel: string;
  netInterestRateColumnLabel: string;
  feeDetails: FeeDetailsPerPlanType[];
};

type SavingsProductHighlightItemType = {
  label: string;
  value: string;
};

type SavingsProductHighlightsSectionType = {
  oneDayYieldGross: SavingsProductHighlightItemType;
  oneDayYieldNet: SavingsProductHighlightItemType;
  earnedLastMonth: SavingsProductHighlightItemType;
  lifetimeEarnings: SavingsProductHighlightItemType;
};
type SavingsProductInformationSectionType = {
  fundName: string;
  fundManager: string;
  isin: string;
  benchmark: string;
  baseCurrency: currenciesConfig.MainCurrencyType;
  income: savingsUniverseConfig.SavingsProductIncomeType;
  distribution: savingsUniverseConfig.SavingsProductDistributionType;
};
type SavingsProductFundQualitySectionType = {
  rating: string;
  creditRatings: { label: string; rating: string }[];
  ratingSubtitle: string;
  risk: savingsUniverseConfig.SavingsProductRiskType;
};

type SavingsProductDataType = {
  highlightsSection: SavingsProductHighlightsSectionType;
  informationSection: SavingsProductInformationSectionType;
  fundQualitySection: SavingsProductFundQualitySectionType;
};

type UserSavingsItemType = {
  savingsProductId: savingsUniverseConfig.SavingsProductType;
  netInterestRate: string;
  currency: currenciesConfig.MainCurrencyType;
  unrealisedInterest: number;
  displayUnrealisedInterest?: string;
  savingsAmount: number;
  displaySavingsAmount: string;
};
export type UserSavingsType = UserSavingsItemType[];

export default class SavingsProductService {
  // ***************
  // PUBLIC METHODS
  // ***************

  /**
   * @description
   * This method return the aggregated savings amount. This is the value that we want to display to the user.
   * @returns
   * aggregatedSavingsAmount: currentSavingHoldings + pendingBuyAmount - pendingSellAmount
   * availableToSell: currentSavingHoldings - pendingSellAmount
   */
  public static async getAggregatedSavingsAmount(
    portfolio: PortfolioDocument,
    savingsProductId: savingsUniverseConfig.SavingsProductType
  ): Promise<{ aggregatedSavingsAmount: number; availableToSell: number }> {
    const userId = portfolio.populated("owner")
      ? (portfolio.owner as UserDocument).id
      : (portfolio.owner as mongoose.Types.ObjectId).toString();
    const pendingTransactions = await TransactionService.getSavingsTransactionsWithPotentiallyPendingAmounts(
      userId,
      savingsProductId
    );

    /**
     * Calculate the pending buy/sell amount: by summing up the pending amount of each transaction.
     * The pending amount is the different of the transaction amount minus internally filled amount.
     * This amount is not yet reflected in the portfolio savings amount and that's why it's called pending.
     */
    const pendingBuyAmount = pendingTransactions
      .filter(({ category }) => category === "SavingsTopupTransaction")
      .reduce((sum, transaction: SavingsTopupTransactionDocument) => {
        const pendingAmount = Decimal.sub(transaction.consideration.amount, transaction.internallyFilledAmount);
        return sum.plus(pendingAmount);
      }, new Decimal(0));
    const pendingSellAmount = pendingTransactions
      .filter(({ category }) => category === "SavingsWithdrawalTransaction")
      .reduce((sum, transaction: SavingsWithdrawalTransactionDocument) => {
        const pendingAmount = Decimal.sub(transaction.consideration.amount, transaction.internallyFilledAmount);
        return sum.plus(pendingAmount);
      }, new Decimal(0));
    const currentSavingsAmount = new Decimal(portfolio.savings.get(savingsProductId)?.amount ?? 0);

    const aggregatedSavingsAmount = currentSavingsAmount.plus(pendingBuyAmount).minus(pendingSellAmount);
    if (aggregatedSavingsAmount.lt(0)) {
      logger.error(`Aggregated savings amount for ${portfolio.id} portfolio is less than 0!!!`, {
        module: "SavingsProductService",
        method: "getAggregatedSavingsAmount",
        data: {
          currentSavingsAmount,
          pendingBuyAmount,
          pendingSellAmount
        }
      });
      throw new Error(`Aggregated savings amount for ${portfolio.id} portfolio is less than 0!!!`);
    }

    const response = {
      aggregatedSavingsAmount: aggregatedSavingsAmount.toNumber(),
      availableToSell: currentSavingsAmount.minus(pendingSellAmount).toNumber()
    };

    logger.info(`Found ${response.aggregatedSavingsAmount} aggregated savings amount for portfolio`, {
      module: "SavingsProductService",
      method: "getAggregatedSavingsAmount",
      data: {
        portfolio: portfolio.id,
        ...response
      }
    });
    return response;
  }

  public static async getSavingsProducts(populate: { currentTicker: boolean }): Promise<SavingsProductDocument[]> {
    const populateString = DbUtil.getPopulationString(populate);

    const savingsProducts = await SavingsProduct.find().populate(populateString);

    return savingsProducts.filter((savingsProduct) => !!SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProduct.commonId]);
  }

  public static async getSavingsProduct(
    commonId: savingsUniverseConfig.SavingsProductType,
    populate: { currentTicker: boolean }
  ): Promise<SavingsProductDocument> {
    const populateString = DbUtil.getPopulationString(populate);

    return SavingsProduct.findOne({ commonId }).populate(populateString);
  }

  public static async getSavingsProductsDict(
    key: SavingsProductDictKeyType,
    populateTicker: boolean
  ): Promise<{ [key: string]: SavingsProductDocument }> {
    const savingsProducts: SavingsProductDocument[] = await SavingsProductService.getSavingsProducts({
      currentTicker: populateTicker
    });

    if (key === "isin") {
      return Object.fromEntries(
        savingsProducts.map((savingsProduct) => [
          SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProduct.commonId].isin,
          savingsProduct
        ])
      );
    }

    return Object.fromEntries(savingsProducts.map((savingsProduct) => [savingsProduct.get(key), savingsProduct]));
  }

  public static async isActive(
    commonId: savingsUniverseConfig.SavingsProductType,
    side: OrderSideType
  ): Promise<boolean> {
    const savingsProduct: SavingsProductDocument = await SavingsProduct.findOne({
      commonId
    });

    return side === "Buy" ? savingsProduct.buyLine.active : savingsProduct.sellLine.active;
  }

  public static async getUserSavingsProductFeeDetails(
    user: UserDocument,
    savingsProductId: savingsUniverseConfig.SavingsProductType
  ): Promise<SavingsProductFeeDetailsType> {
    const [savingsProduct, subscription] = await Promise.all([
      SavingsProduct.findOne({ commonId: savingsProductId }).populate("currentTicker"),
      SubscriptionService.getSubscription(user.id)
    ]);

    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);
    const { oneDayYield } = savingsProduct.currentTicker;
    const savingsProductFeeDetails = this.getSavingsProductFeeDetails(savingsProduct, locale);

    if (subscription?.plan) {
      const savingsProductFees = SAVINGS_PRODUCT_FEE_RATES[savingsProductId];
      const netInterestRateOfCurrentPlan = Decimal.sub(
        oneDayYield,
        Decimal.mul(savingsProductFees[subscription.plan], 100)
      )
        .toDecimalPlaces(2)
        .div(100)
        .toNumber();
      savingsProductFeeDetails["netInterestRateOfCurrentPlan"] = formatPercentage(
        netInterestRateOfCurrentPlan,
        locale
      );
    }

    return savingsProductFeeDetails;
  }

  public static getSavingsProductFeeDetails(
    savingProduct: SavingsProductDocument,
    userLocale: localeConfig.LocaleType
  ): SavingsProductFeeDetailsType {
    const { oneDayYield } = savingProduct.currentTicker;

    const { fee: fundManagerFeePercentage, fundManager } = SAVINGS_PRODUCT_CONFIG_GLOBAL[savingProduct.commonId];
    const savingsProductFees = SAVINGS_PRODUCT_FEE_RATES[savingProduct.commonId];
    const feeDetails: FeeDetailsPerPlanType[] = Object.entries(savingsProductFees).map(
      ([plan, annualFee]: [plansConfig.PlanType, number]) => {
        const annualFeePercentage = Decimal.mul(annualFee, 100);

        return {
          plan: plan,
          fundManagerAnnualFeePercentage: formatPercentage(
            Decimal.div(fundManagerFeePercentage, 100).toNumber(),
            userLocale
          ),
          wealthyhoodAnnualFeePercentage: formatPercentage(annualFeePercentage.div(100).toNumber(), userLocale),
          netInterestRate: `${formatPercentage(Decimal.sub(oneDayYield, annualFeePercentage).div(100).toNumber(), userLocale)} p.a.`,
          netInterestRateValue: Decimal.sub(oneDayYield, annualFeePercentage).toDecimalPlaces(2).toNumber()
        };
      }
    );

    const grossInterestRate = Decimal.add(fundManagerFeePercentage, oneDayYield);

    return {
      grossInterestRate: formatPercentage(grossInterestRate.div(100).toNumber(), userLocale),
      planColumnLabel: "Your plan",
      fundManagerFeeColumnLabel: `${fundManager} fee annually`,
      wealthyhoodFeeColumnLabel: "Our fee annually",
      netInterestRateColumnLabel: "Your 1-day yield (net)",
      feeDetails
    };
  }

  public static async getSavingsProductData(
    userId: string,
    savingsProductId: savingsUniverseConfig.SavingsProductType
  ): Promise<SavingsProductDataType> {
    const [savingsProduct, subscription, settledDividends, user] = await Promise.all([
      SavingsProduct.findOne({ commonId: savingsProductId }).populate("currentTicker"),
      SubscriptionService.getSubscription(userId),
      SavingsDividendTransaction.find({ owner: userId, status: "Settled" }),
      UserService.getUser(userId, { addresses: false, portfolios: false })
    ]);
    const oneDayYield = savingsProduct.currentTicker.oneDayYield;
    const {
      fee: fundManagerFeePercentage,
      fundName,
      fundManager,
      isin,
      benchmark,
      baseCurrency,
      income,
      distribution,
      rating,
      creditRatings,
      ratingSubtitle,
      risk
    } = SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProductId];

    const oneDayYieldGross = Decimal.add(oneDayYield, fundManagerFeePercentage);

    const planFeePercentage = Decimal.mul(SAVINGS_PRODUCT_FEE_RATES[savingsProductId][subscription.plan], 100);
    const oneDayYieldNet = Decimal.sub(oneDayYield, planFeePercentage).toDecimalPlaces(2).div(100).toNumber();

    const lastMonth = DateUtil.getYearAndMonth(DateUtil.getFirstDayOfLastMonth());
    const dividendOfLastMonth = settledDividends.find((dividend) => dividend.dividendMonth === lastMonth);
    const interestEarnedLastMonth = Decimal.div(dividendOfLastMonth?.consideration?.amount ?? 0, 100)
      .toDecimalPlaces(2)
      .toNumber();
    const interestEarnedLifetime = settledDividends
      .reduce((sum, savingsDividend) => sum.plus(savingsDividend.consideration.amount), new Decimal(0))
      .div(100)
      .toNumber();
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    const response: SavingsProductDataType = {
      highlightsSection: {
        oneDayYieldGross: {
          label: "1-day yield (gross)",
          value: `${formatPercentage(oneDayYieldGross.div(100).toNumber(), locale)} p.a.`
        },
        oneDayYieldNet: {
          label: "1-day yield (net)",
          value: `${formatPercentage(oneDayYieldNet, locale)} p.a.`
        },
        earnedLastMonth: {
          label: "Earned last month",
          value: `${CurrencyUtil.formatCurrency(interestEarnedLastMonth, user.currency, locale)}`
        },
        lifetimeEarnings: {
          label: "Lifetime earnings",
          value: `${CurrencyUtil.formatCurrency(interestEarnedLifetime, user.currency, locale)}`
        }
      },
      informationSection: {
        fundName,
        fundManager,
        isin,
        benchmark,
        baseCurrency,
        income,
        distribution
      },
      fundQualitySection: {
        rating,
        creditRatings,
        ratingSubtitle,
        risk
      }
    };

    return response;
  }

  public static async getUserSavings(owner: string, date: Date = new Date(Date.now())): Promise<UserSavingsType> {
    const [user, savingsProductDict] = await Promise.all([
      UserService.getUser(owner, { subscription: true, portfolios: true }),
      SavingsProductService.getSavingsProductsDict("commonId", true)
    ]);

    const savingsUniverse = ConfigUtil.getSavingsUniverse(user.companyEntity);
    const savingsProductAvailableToUser = Object.keys(
      savingsUniverse
    ) as savingsUniverseConfig.SavingsProductType[];

    return Promise.all(
      savingsProductAvailableToUser.map((savingsProductId) =>
        SavingsProductService._getUserSavingsItem(savingsProductDict[savingsProductId], user, date)
      )
    );
  }

  // ***************
  // PRIVATE METHODS
  // ***************

  private static async _getUserSavingsItem(
    savingsProduct: SavingsProductDocument,
    user: UserDocument,
    date: Date
  ): Promise<UserSavingsItemType> {
    const subscription = user.subscription as SubscriptionDocument;
    const portfolio = user.portfolios[0] as PortfolioDocument;
    const firstDayOfLastMonth = DateUtil.getFirstDayOfLastMonth(date);

    const [tickers, dividendOfLastMonth, { aggregatedSavingsAmount }] = await Promise.all([
      DailyPortfolioSavingsTicker.find({
        portfolio: portfolio.id,
        date: { $gte: firstDayOfLastMonth }
      }),
      SavingsDividendTransaction.findOne({
        portfolio: portfolio.id,
        savingsProduct: savingsProduct.commonId,
        dividendMonth: DateUtil.getYearAndMonth(firstDayOfLastMonth)
      }),
      SavingsProductService.getAggregatedSavingsAmount(portfolio, savingsProduct.commonId)
    ]);

    // 1. Calculate net interest
    const { oneDayYield } = savingsProduct.currentTicker;
    const planFeePercentage = Decimal.mul(
      SAVINGS_PRODUCT_FEE_RATES[savingsProduct.commonId][subscription.plan],
      100
    );
    const netInterestRate = Decimal.sub(oneDayYield, planFeePercentage);

    // 2. Calculate unrealised interest
    /**
     * A user's unrealised interest is the sum of daily accruals from the last month minus the dividend for the last month. In some cases we aggregate
     * multiple small dividends into a single dividend transaction. In those cases, we need to subtract the sum of the aggregated dividends from
     * the dividendOfLastMonth that they were aggregated into to get the actual dividend.
     *
     * Because the dividends are paid in the first days of the month, we still want to display the interest that was earned for the past month
     * but is not realised yet.
     *
     * In the case where we aggregate very low savings dividend transaction together into the dividendOfLastMonth
     * we can end up with negative unrealised interest. In this case, we set it to 0.
     */
    const aggregatedSavingsDividendTransactions = await SavingsDividendTransaction.find({
      cancelledBy: dividendOfLastMonth?.id
    });
    const sumOfAggregatedSavingsDividends = aggregatedSavingsDividendTransactions.reduce(
      (sum, transaction) => sum.plus(transaction?.consideration?.amount ?? 0),
      new Decimal(0)
    );

    const actualDividendOfLastMonth = Decimal.sub(
      dividendOfLastMonth?.consideration?.amount ?? 0,
      sumOfAggregatedSavingsDividends
    );

    const accrualSumFromLastMonth = tickers.reduce((sum, ticker) => sum.plus(ticker.dailyAccrual), new Decimal(0));
    let unrealisedInterest = accrualSumFromLastMonth.minus(actualDividendOfLastMonth).div(100).toDecimalPlaces(2);

    // Unrealised interest should not be able to ne negative, so we set it to 0
    // and log it in case it happens.
    if (unrealisedInterest.lt(0)) {
      unrealisedInterest = new Decimal(0);

      logger.error(
        `Unrealised interest is negative for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`,
        {
          module: "SavingsProductService",
          method: "_getUserSavingsItem",
          data: {
            savingsProductId: savingsProduct.commonId,
            portfolio: portfolio.id,
            unrealisedInterest: unrealisedInterest.toNumber()
          }
        }
      );
      captureException(
        new Error(
          `Unrealised interest is negative for ${savingsProduct.commonId} savings product of ${portfolio.id} portfolio`
        )
      );
    }

    // 3. Retrieve aggregated savings amount
    const savingsAmount = Decimal.div(aggregatedSavingsAmount, 100).toNumber();
    const locale = ConfigUtil.getDefaultUserLocale(user.residencyCountry);

    return {
      savingsProductId: savingsProduct.commonId,
      netInterestRate: formatPercentage(netInterestRate.div(100).toNumber(), locale),
      currency: savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL[savingsProduct.commonId].baseCurrency,
      unrealisedInterest: unrealisedInterest.toNumber(),
      displayUnrealisedInterest: unrealisedInterest.gt(0)
        ? `+${CurrencyUtil.formatCurrency(unrealisedInterest.toDecimalPlaces(2).toNumber(), user.currency, ConfigUtil.getDefaultUserLocale(user.residencyCountry))}`
        : undefined,
      displaySavingsAmount: CurrencyUtil.formatCurrency(savingsAmount, user.currency, locale),
      savingsAmount: savingsAmount
    };
  }
}
